from getpass import getpass
import sys

from pydantic import ValidationError
from sqladmin import Admin, ModelView
from sqladmin.authentication import AuthenticationBackend
from sqlmodel import select
from starlette.requests import Request

from app.auth import get_password_hash, verify_password
from app.config import SECRET_KEY
from app.database import engine, get_session
from app.models import *
from app.utils import validate_password


# Register your table(s) here.
class AdminAuth(AuthenticationBackend):

    async def login(self, request: Request) -> bool:
        form = await request.form()
        email = form.get("username")
        password = form.get("password")
        session = get_session()
        user = session.exec(select(User).where(User.email == email)).first()
        if user and verify_password(password, user.hashed_password):
            if user.is_admin:
                request.session.update({"token": user.email})
                return True
        else:
            False

    async def logout(self, request: Request) -> bool:
        request.session.clear()
        return True

    async def authenticate(self, request: Request) -> bool:
        token = request.session.get("token")
        return token is not None


def create_superuser():
    first_name = input("Enter first name: ")
    last_name = input("Enter last name: ")
    email = input("Enter email: ")

    try:
        email = EmailValidator(email=email).email
    except ValidationError:
        sys.stderr.write("Enter a valid email address\n")
        sys.exit(1)

    password = getpass("Enter password: ")
    confirm_password = getpass("Confirm password: ")

    if password != confirm_password:
        sys.stderr.write("Passwords do not match.\n")
        sys.exit(1)
    validate_password(password)
    hashed_password = get_password_hash(password)

    session = get_session()
    try:
        admin = session.exec(select(User).where(User.email == email)).first()
        if admin:
            sys.stderr.write("User already exists.\n")
            sys.exit(1)
        admin = User(
            first_name=first_name,
            last_name=last_name,
            email=email,
            hashed_password=hashed_password,
            is_admin=True,
            is_superuser=True,
        )
        session.add(admin)
        session.commit()
        sys.stdout.write("Admin user created successfully.\n")
    finally:
        session.close()
    sys.exit(0)


class UserAdmin(ModelView, model=User):
    name = "USER"
    name_plural = "USERS"
    column_exclude_list = ["hashed_password"]
    form_excluded_columns = ["hashed_password"]




def create_admin(app):
    authentication_backend = AdminAuth(secret_key=SECRET_KEY)
    admin = Admin(
        app=app,
        engine=engine,
        authentication_backend=authentication_backend,
        title="HISA Admin Panel",
        base_url="/admin",
    )

    admin.add_view(UserAdmin)
    return admin


if __name__ == "__main__":
    create_superuser()
