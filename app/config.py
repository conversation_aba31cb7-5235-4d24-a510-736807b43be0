import os
from pytz import timezone

from decouple import config


# Add the application configuration(s) here.
BASE_DIR = os.getcwd()

ACCESS_TOKEN_EXPIRE_MINUTES = config("ACCESS_TOKEN_EXPIRE_MINUTES", cast=int)

DATABASE_URL = config("DATABASE_URL", cast=str)

REFRESH_TOKEN_EXPIRE_MINUTES = config("REFRESH_TOKEN_EXPIRE_MINUTES", cast=int)

SECRET_KEY = config("SECRET_KEY", cast=str)

TIMEZONE = timezone("Africa/Lagos")
