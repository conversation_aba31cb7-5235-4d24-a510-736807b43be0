import pandas as pd

from app import (
    clean_merge_hisa,
    reconcile_logs,
    strip_234,
)


def prepare_mtn_telco(df: pd.DataFrame):
    df = df.copy()
    expected_headers = [
        "ClientReference",
        "ReceiverMSISDN",
        "TransactionAmount",
        "EndTime",
    ]
    sheet_columns = [column for column in df.columns]
    retrieved_columns = [
        column for column in sheet_columns if column in expected_headers
    ]
    if len(retrieved_columns) < 4:
        return f"The following headers: {expected_headers} are expected for MTN telco."

    # Add TransactionStatus column based on ResultStatus
    df["TransactionStatus"] = "FAILED"
    df.loc[df["ResultStatus"].str.upper() == "SUCCESS", "TransactionStatus"] = "SUCCESS"

    if df.empty:
        return "The uploaded sheet is empty."

    df["TransactionType"] = "AIRTIME"
    if "TransactionProfile" in df.columns:
        df.loc[df["TransactionProfile"] == "DATA_BUNDLE", "TransactionType"] = "DATA"
        df.loc[df["TransactionProfile"] == "PREPAID_TOP UP", "TransactionType"] = "AIRTIME"
        df = df[df["TransactionProfile"] != "CREDIT_TRANSFER"]

    df["TxnId"] = df["ClientReference"].astype(str).str.replace(".", "", regex=False)
    df["TxnId"] = df["TxnId"].fillna("NO_TXN_ID")
    df["MSISDN"] = df["ReceiverMSISDN"].astype(str).apply(strip_234)
    df["Amount"] = pd.to_numeric(df["TransactionAmount"], errors="coerce").fillna(0).round()
    df["Date"] = pd.to_datetime(
        df["EndTime"],
        format="%Y-%m-%d %H:%M:%S",
        errors="coerce",
    )
    df["Date"] = pd.to_datetime(df["Date"]).dt.tz_localize(None)
    df = df[["TxnId", "MSISDN", "Amount", "Date", "TransactionStatus", "TransactionType"]]
    return df.sort_values("Date")


if __name__ == "__main__":
    hisa_admin = clean_merge_hisa("MTN", "8th.xlsx")
    mtn_telco = pd.read_excel("total_transaction_agent (8th).xlsx")
    cleaned_telco = prepare_mtn_telco(mtn_telco)
    result = reconcile_logs("MTN", hisa_admin, cleaned_telco, "2025-04-08")

    print("\n\n")
    print("Matching Statistics")
    print(f"{result['matching_statistics']}\n\n")
    print(f"HISA Total: {result['hisa_total']}")
    print(f"MTN Telco Total: {result['telco_total']}")
    print(f"Difference: {result['difference']}")
    print(f"\nTransactions in MTN but missing in HISA: {len(result['missing_in_hisa'])}")
    print(f"Transactions in HISA but missing in MTN: {len(result['extra_in_hisa'])}\n")
