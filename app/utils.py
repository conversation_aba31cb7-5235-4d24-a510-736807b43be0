import os
from pathlib import Path
from string import punctuation
from typing import Optional

from fastapi import HTTPException, status
import pandas as pd

from app.config import BASE_DIR


# reusable function(s).
def strip_234(msisdn):
    msisdn = str(msisdn)
    msisdn = msisdn.replace(".", "")
    return msisdn[-10:]


def prepare_hisa(mno: str, df: pd.DataFrame, success_value="SUCCESSFUL"):
    mno_ids = {
        "MTN": 1,
        "AIRTEL": 2,
        "GLO": 4
    }
    mno_id = mno_ids.get(mno.upper())
    df = df.copy()
    if df.empty:
        return "The uploaded sheet is empty."

    df["TransactionStatus"] = "FAILED"
    df.loc[df["Status"].str.upper() == success_value.upper(), "TransactionStatus"] = "SUCCESS"
    if "Network" in df.columns:
        # Convert Network column to string type before string operations
        df["Network"] = df["Network"].astype(str)
        df = df[(df["Network"].str.upper() == mno.upper()) | (df["Network"] == str(mno_id))]
    else:
        df["Network"] = mno.upper()
    df["TransactionType"] = "AIRTIME"
    if "Type" in df.columns:
        df.loc[df["Type"].str.upper() == "DATA", "TransactionType"] = "DATA"
    if "Txn ID" in df.columns:
        df["TxnId"] = df["Txn ID"].astype(str)
    else:
        df["TxnId"] = "NOT_SUPPLIED"

    df["MSISDN"] = df["MSISDN"].astype(str).apply(strip_234)
    df["Amount"] = df["Total Amount (₦)"].round()
    df["Date"] = pd.to_datetime(df["Date"], format='ISO8601')
    df["Date"] = pd.to_datetime(df["Date"]).dt.tz_localize(None)
    columns_to_keep = ["Network", "TxnId", "MSISDN", "Amount", "Date", "TransactionStatus", "TransactionType"]

    if "User" in df.columns:
        df["User"] = df["User"].astype(str)
        columns_to_keep.append("User")
    else:
        df["User"] = "NOT_SUPPLIED"
        columns_to_keep.append("User")

    if "Channel" in df.columns:
        df["Channel"] = df["Channel"].astype(str)
        columns_to_keep.append("Channel")
    else:
        df["Channel"] = "NOT_SUPPLIED"
        columns_to_keep.append("Channel")

    df = df[columns_to_keep]
    return df.sort_values("Date")


def clean_merge_hisa(mno, source, hisa_admin_sheet):
    hisa_sheets = pd.ExcelFile(hisa_admin_sheet)
    available_sheets = hisa_sheets.sheet_names
    sheets = dict()
    count = 1
    for hisa_sheet in available_sheets:
        parsed_file = hisa_sheets.parse(hisa_sheet)
        cleaned_file = prepare_hisa(mno, parsed_file)
        if not isinstance(cleaned_file, str):
            cleaned_file["Source"] = source
            sheets[f"hisa{count}"] = cleaned_file
            count += 1
    if not sheets:
        return pd.DataFrame()  # Return empty DataFrame if no sheets were processed
    return pd.concat(list(sheets.values()))


def format_currency(amount: int):
    """
    Returns formatted monetary values.
    """
    if amount is None:
        currency = "NGN0"
    else:
        currency = "NGN{:,}".format(amount)
    return currency


def make_file(report_category: str, report_type: str, file_name: str):
    """
    Returns:
        - The file path & the file.
    """
    file_path = os.path.join(
        BASE_DIR, f"reports/{report_category}/{report_type}_report"
    )
    file = f"{file_path}/{file_name}"
    return {"file_path": file_path, "file": file}


def reconcile_logs(
    mno: str,
    hisa_admin: pd.DataFrame,
    telco_df: pd.DataFrame,
    target_date: str,
    use_transaction_id: Optional[bool] = False
):
    target_date = pd.to_datetime(target_date).date()
    local = hisa_admin
    local_day = local[local["Date"].dt.date == target_date].copy()
    telco_clean = telco_df

    # Check if telco_clean is a string (error message) or DataFrame
    if isinstance(telco_clean, str):
        raise ValueError(f"Error processing telco data: {telco_clean}")

    telco_day = telco_clean[telco_clean["Date"].dt.date == target_date].copy()

    # Calculate total counts and values for all transactions (successful and failed)
    hisa_all_count = len(local_day)
    hisa_all_value = local_day["Amount"].sum()
    telco_all_count = len(telco_day)
    telco_all_value = telco_day["Amount"].sum()

    # Calculate counts and values by transaction type (AIRTIME and DATA)
    hisa_airtime_count = len(local_day[local_day["TransactionType"] == "AIRTIME"])
    hisa_airtime_value = local_day[local_day["TransactionType"] == "AIRTIME"]["Amount"].sum()
    hisa_data_count = len(local_day[local_day["TransactionType"] == "DATA"])
    hisa_data_value = local_day[local_day["TransactionType"] == "DATA"]["Amount"].sum()

    telco_airtime_count = len(telco_day[telco_day["TransactionType"] == "AIRTIME"])
    telco_airtime_value = telco_day[telco_day["TransactionType"] == "AIRTIME"]["Amount"].sum()
    telco_data_count = len(telco_day[telco_day["TransactionType"] == "DATA"])
    telco_data_value = telco_day[telco_day["TransactionType"] == "DATA"]["Amount"].sum()

    # Calculate counts and values for failed transactions
    hisa_failed_count = len(local_day[local_day["TransactionStatus"] == "FAILED"])
    hisa_failed_value = local_day[local_day["TransactionStatus"] == "FAILED"]["Amount"].sum()
    telco_failed_count = len(telco_day[telco_day["TransactionStatus"] == "FAILED"])
    telco_failed_value = telco_day[telco_day["TransactionStatus"] == "FAILED"]["Amount"].sum()

    # Filter for successful transactions for reconciliation
    local_day_success = local_day[local_day["TransactionStatus"] == "SUCCESS"].copy()
    telco_day_success = telco_day[telco_day["TransactionStatus"] == "SUCCESS"].copy()

    # Calculate successful transactions by type
    hisa_success_airtime_count = len(local_day_success[local_day_success["TransactionType"] == "AIRTIME"])
    hisa_success_airtime_value = local_day_success[local_day_success["TransactionType"] == "AIRTIME"]["Amount"].sum()
    hisa_success_data_count = len(local_day_success[local_day_success["TransactionType"] == "DATA"])
    hisa_success_data_value = local_day_success[local_day_success["TransactionType"] == "DATA"]["Amount"].sum()

    telco_success_airtime_count = len(telco_day_success[telco_day_success["TransactionType"] == "AIRTIME"])
    telco_success_airtime_value = telco_day_success[telco_day_success["TransactionType"] == "AIRTIME"]["Amount"].sum()
    telco_success_data_count = len(telco_day_success[telco_day_success["TransactionType"] == "DATA"])
    telco_success_data_value = telco_day_success[telco_day_success["TransactionType"] == "DATA"]["Amount"].sum()

    # print(f"LOCAL DAY (All): {hisa_all_count}")
    # print(f"LOCAL DAY (Success): {len(local_day_success)}")
    # print(f"TELCO DAY (All): {telco_all_count}")
    # print(f"TELCO DAY (Success): {len(telco_day_success)}\n")

    # Create match keys including transaction type when applicable
    if use_transaction_id:
        if mno.upper() in ["MTN", "AIRTEL"]:
            # Include TransactionType in match key for MTN and AIRTEL
            local_day_success["match_key"] = (
                local_day_success["TxnId"] + "_" +
                local_day_success["MSISDN"] + "_" +
                local_day_success["Amount"].astype(str) + "_" +
                local_day_success["TransactionType"]
            )
            telco_day_success["match_key"] = (
                telco_day_success["TxnId"] + "_" +
                telco_day_success["MSISDN"] + "_" +
                telco_day_success["Amount"].astype(str) + "_" +
                telco_day_success["TransactionType"]
            )
        else:
            # For other MNOs, use the original match key
            local_day_success["match_key"] = (
                local_day_success["TxnId"] + "_" +
                local_day_success["MSISDN"] + "_" +
                local_day_success["Amount"].astype(str)
            )
            telco_day_success["match_key"] = (
                telco_day_success["TxnId"] + "_" +
                telco_day_success["MSISDN"] + "_" +
                telco_day_success["Amount"].astype(str)
            )
    else:
        if mno.upper() in ["MTN", "AIRTEL"]:
            # Include TransactionType in match key for MTN and AIRTEL
            local_day_success["match_key"] = (
                local_day_success["MSISDN"] + "_" +
                local_day_success["Amount"].astype(str) + "_" +
                local_day_success["TransactionType"]
            )
            telco_day_success["match_key"] = (
                telco_day_success["MSISDN"] + "_" +
                telco_day_success["Amount"].astype(str) + "_" +
                telco_day_success["TransactionType"]
            )
        else:
            # For other MNOs, use the original match key
            local_day_success["match_key"] = (
                local_day_success["MSISDN"] + "_" +
                local_day_success["Amount"].astype(str)
            )
            telco_day_success["match_key"] = (
                telco_day_success["MSISDN"] + "_" +
                telco_day_success["Amount"].astype(str)
            )

    # Find duplicates in local_day (HISA)
    local_duplicates = local_day_success[local_day_success.duplicated("match_key", keep=False)].copy()

    # Find duplicates in telco_day
    telco_duplicates = telco_day_success[telco_day_success.duplicated("match_key", keep=False)].copy()

    local_keys = set(local_day_success["match_key"])
    telco_keys = set(telco_day_success["match_key"])
    missing_keys = telco_keys - local_keys
    extra_keys = local_keys - telco_keys
    matching_keys = local_keys & telco_keys

    # Get missing and extra records
    # For telco records missing in HISA
    telco_columns = ["TxnId", "MSISDN", "Amount", "Date", "TransactionType"]
    missing_in_hisa = telco_day_success[telco_day_success["match_key"].isin(missing_keys)][telco_columns]

    # For HISA records not found in telco, include User and Channel columns if they exist
    hisa_columns = ["TxnId", "MSISDN", "Amount", "Date", "TransactionType"]
    if "User" in local_day_success.columns:
        hisa_columns.append("User")
    if "Channel" in local_day_success.columns:
        hisa_columns.append("Channel")
    extra_in_hisa = local_day_success[local_day_success["match_key"].isin(extra_keys)][hisa_columns]

    # write duplicate records to file.
    file_maker = make_file(
        report_category=f"{mno}_reconciliation",
        report_type="duplicates",
        file_name=f"{mno}_duplicates_{target_date}.xlsx",
    )
    file_path = file_maker.get("file_path")
    duplicates_file = file_maker.get("file")
    if not os.path.exists(file_path):
        directory = Path(file_path)
        directory.mkdir(parents=True, exist_ok=True)
    with pd.ExcelWriter(duplicates_file) as writer:
        if not local_duplicates.empty:
            local_duplicates.to_excel(writer, sheet_name="HISA_Duplicates", index=False)
        if not telco_duplicates.empty:
            telco_duplicates.to_excel(writer, sheet_name=f"{mno}_Duplicates", index=False)
        # Add empty sheet if no data to write
        if local_duplicates.empty and telco_duplicates.empty:
            pd.DataFrame({'Message': ['No duplicate records found']}).to_excel(writer, sheet_name="No_Duplicates", index=False)

    # write missing records to file.
    file_maker = make_file(
        report_category=f"{mno}_reconciliation",
        report_type="missing",
        file_name=f"{mno}_missing_{target_date}.xlsx",
    )
    file_path = file_maker.get("file_path")
    missing_file = file_maker.get("file")
    if not os.path.exists(file_path):
        directory = Path(file_path)
        directory.mkdir(parents=True, exist_ok=True)
    with pd.ExcelWriter(missing_file) as writer:
        if not extra_in_hisa.empty:
            extra_in_hisa.to_excel(writer, sheet_name="HISA_Missing", index=False)
        if not missing_in_hisa.empty:
            missing_in_hisa.to_excel(writer, sheet_name=f"{mno}_Missing", index=False)
        # Add empty sheet if no data to write
        if extra_in_hisa.empty and missing_in_hisa.empty:
            pd.DataFrame({'Message': ['No missing records found']}).to_excel(writer, sheet_name="No_Missing", index=False)

    # Generate user consumption report
    user_consumption_file = generate_user_consumption_report(local_day, mno, target_date)

    # Calculate success-only values for reconciliation
    hisa_success_value = local_day_success["Amount"].sum()
    telco_success_value = telco_day_success["Amount"].sum()

    return {
        "hisa_all_count": hisa_all_count,
        "hisa_all_value": format_currency(hisa_all_value),
        "telco_all_count": telco_all_count,
        "telco_all_value": format_currency(telco_all_value),
        "hisa_success_count": len(local_day_success),
        "telco_success_count": len(telco_day_success),
        "matching_statistics": {
            "total_unique_in_HISA": len(local_keys),
            "total_unique_in_TELCO": len(telco_keys),
            "matching_transactions": len(matching_keys),
            "match_combination_keys_in_HISA_but_not_in_TELCO": len(local_keys - telco_keys),
            "match_combination_keys_in_TELCO_but_not_in_HISA": len(telco_keys - local_keys),
        },
        "hisa_total": format_currency(hisa_success_value),
        "telco_total": format_currency(telco_success_value),
        "difference": format_currency(
            telco_success_value - hisa_success_value
        ),
        "missing_in_hisa": missing_in_hisa,
        "extra_in_hisa": extra_in_hisa,
        "local_duplicates": local_duplicates,
        "telco_duplicates": telco_duplicates,
        "duplicates_file": duplicates_file,
        "missing_file": missing_file,
        "user_consumption_file": user_consumption_file,
        # New metrics
        "hisa_airtime_count": hisa_airtime_count,
        "hisa_airtime_value": format_currency(hisa_airtime_value),
        "hisa_data_count": hisa_data_count,
        "hisa_data_value": format_currency(hisa_data_value),
        "telco_airtime_count": telco_airtime_count,
        "telco_airtime_value": format_currency(telco_airtime_value),
        "telco_data_count": telco_data_count,
        "telco_data_value": format_currency(telco_data_value),
        "hisa_failed_count": hisa_failed_count,
        "hisa_failed_value": format_currency(hisa_failed_value),
        "telco_failed_count": telco_failed_count,
        "telco_failed_value": format_currency(telco_failed_value),
        "hisa_success_airtime_count": hisa_success_airtime_count,
        "hisa_success_airtime_value": format_currency(hisa_success_airtime_value),
        "hisa_success_data_count": hisa_success_data_count,
        "hisa_success_data_value": format_currency(hisa_success_data_value),
        "telco_success_airtime_count": telco_success_airtime_count,
        "telco_success_airtime_value": format_currency(telco_success_airtime_value),
        "telco_success_data_count": telco_success_data_count,
        "telco_success_data_value": format_currency(telco_success_data_value)
    }


def generate_user_consumption_report(local_day, mno, target_date):
    """
    Generates a report showing the amount consumed by each user based on transaction type.

    Args:
        local_day (pd.DataFrame): DataFrame containing HISA transactions for the target date
        mno (str): Mobile Network Operator name
        target_date (str): Target date for the report
    Returns:
        str: Path to the generated report file
    """
    # Create a copy of the DataFrame to avoid modifying the original
    df = local_day.copy()

    # Check if User column exists
    if "User" not in df.columns:
        df["User"] = "Unknown"

    # Group by User and TransactionType to calculate total amount
    user_consumption = df.groupby(["User", "TransactionType", "Source"]).agg({
        "Amount": ["sum", "count"],
        "TransactionStatus": lambda x: (x == "SUCCESS").sum()  # Count successful transactions
    }).reset_index()
    user_consumption.columns = [
        "User", "TransactionType", "Source", "TotalAmount", "TransactionCount", "SuccessfulTransactions"
    ]
    user_consumption["SuccessRate"] = (
        user_consumption["SuccessfulTransactions"] /
        user_consumption["TransactionCount"] * 100
    ).round(2)

    file_maker = make_file(
        report_category=f"{mno}_reconciliation",
        report_type="user_consumption",
        file_name=f"{mno}_user_consumption_{target_date}.xlsx",
    )
    file_path = file_maker.get("file_path")
    report_file = file_maker.get("file")

    if not os.path.exists(file_path):
        directory = Path(file_path)
        directory.mkdir(parents=True, exist_ok=True)

    # Write to Excel with multiple sheets
    with pd.ExcelWriter(report_file) as writer:
        # Summary sheet with all data
        user_consumption.to_excel(writer, sheet_name="Summary", index=False)

        # Create separate sheets for each transaction type
        for tx_type in user_consumption["TransactionType"].unique():
            type_data = user_consumption[user_consumption["TransactionType"] == tx_type]
            type_data.to_excel(writer, sheet_name=f"{tx_type}_Transactions", index=False)

        # Create separate sheets for each source (hisa1, hisa2)
        for source in user_consumption["Source"].unique():
            source_data = user_consumption[user_consumption["Source"] == source]
            source_data.to_excel(writer, sheet_name=f"{source}_Data", index=False)

    return report_file


def validate_password(password: str):
    """
    Validates the given password based on the criteria.
    Args:
        password (str): The password string to be validated.
    Raises:
        HTTPException:
        if the password fails to meet any of the following criteria:
        - The length of the password is less than 8 characters.
        - The password does not contain at least one numeric digit.
        - The password does not contain at least one uppercase character.
        - The password does not contain at least one lowercase character.
        - The password does not contain at least one special character.
    Returns:
        bool: True if the password passes all validation criteria.
    """
    special_characters = list(punctuation)

    if len(password) < 8:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="The password length cannot be less than 8 characters.",
        )
    if not any(char.isdigit() for char in password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="The password should have at least one numeric digit.",
        )
    if not any(char.isupper() for char in password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="The password should have at least one uppercase character.",
        )
    if not any(char.islower() for char in password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="The password should have at least one lowercase character.",
        )
    if not any(char in special_characters for char in password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="The password should have at least one special character.",
        )
    return True
