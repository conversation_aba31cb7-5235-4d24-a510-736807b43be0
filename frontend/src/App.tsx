import React, { useState, useEffect } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, Col, <PERSON>, <PERSON><PERSON>, Al<PERSON>, Mo<PERSON>, <PERSON>, Toast, ToastContainer } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';
import axios from 'axios';
import './App.css';
import ReconciliationResults from './components/ReconciliationResults';

// Use the existing VITE_API_BASE_URL environment variable
const baseURL = import.meta.env.DEV
  ? 'http://localhost:8080'
  : import.meta.env.VITE_API_BASE_URL;

// Import ReconciliationResult from types.ts
import { ReconciliationResult } from './types';

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [username, setUsername] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [authError, setAuthError] = useState<string>('');
  const [token, setToken] = useState<string>(() => {
    // Initialize token from localStorage if it exists
    return localStorage.getItem('authToken') || '';
  });

  const [selectedProvider, setSelectedProvider] = useState<string>('mtn');
  const [targetDate, setTargetDate] = useState<string>('');
  const [hisaFile, setHisaFile] = useState<File | null>(null);
  const [hisaFile2, setHisaFile2] = useState<File | null>(null);
  const [telcoFile, setTelcoFile] = useState<File | null>(null);
  const [telcoFile2, setTelcoFile2] = useState<File | null>(null);
  const [useTransactionId, setUseTransactionId] = useState<boolean>(false);
  const [result, setResult] = useState<ReconciliationResult | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [showErrorModal, setShowErrorModal] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [showSuccessToast, setShowSuccessToast] = useState<boolean>(false);

  // Add Modal for displaying error messages
  const ErrorModal: React.FC = () => (
    <Modal show={showErrorModal} onHide={() => setShowErrorModal(false)}>
      <Modal.Header closeButton className="bg-danger text-white">
        <Modal.Title>
          <i className="bi bi-exclamation-triangle-fill me-2"></i>
          Form Validation Error
        </Modal.Title>
      </Modal.Header>
      <Modal.Body className="p-4">
        <div className="d-flex align-items-center mb-3">
          <div className="bg-danger text-white rounded-circle p-2 me-3">
            <i className="bi bi-x-lg"></i>
          </div>
          <div>
            <h5 className="mb-1">Please check your form</h5>
            <p className="text-muted mb-0">The following issues need to be resolved:</p>
          </div>
        </div>
        <div
          className="alert alert-danger"
          dangerouslySetInnerHTML={{ __html: errorMessage }}
        />
      </Modal.Body>
      <Modal.Footer>
        <Button variant="outline-secondary" onClick={() => setShowErrorModal(false)}>
          <i className="bi bi-x me-2"></i>
          Close
        </Button>
        <Button variant="danger" onClick={() => setShowErrorModal(false)}>
          <i className="bi bi-check2 me-2"></i>
          Fix Issues
        </Button>
      </Modal.Footer>
    </Modal>
  );

  // Success Toast component
  const SuccessToast: React.FC = () => (
    <ToastContainer position="top-end" className="p-3" style={{ zIndex: 1060 }}>
      <Toast
        show={showSuccessToast}
        onClose={() => setShowSuccessToast(false)}
        delay={5000}
        autohide
        bg="success"
      >
        <Toast.Header closeButton={false}>
          <i className="bi bi-check-circle-fill text-success me-2"></i>
          <strong className="me-auto">Success</strong>
          <button
            type="button"
            className="btn-close"
            onClick={() => setShowSuccessToast(false)}
            aria-label="Close"
          ></button>
        </Toast.Header>
        <Toast.Body className="text-white">
          <strong>All fields are valid!</strong> Processing your request...
        </Toast.Body>
      </Toast>
    </ToastContainer>
  );

  useEffect(() => {
    // Check if we have a token on component mount
    const storedToken = localStorage.getItem('authToken');
    if (storedToken) {
      setToken(storedToken);
      setIsAuthenticated(true);
      // Remove this line since we're not using a login modal
    }
  }, []);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setAuthError('');

    try {
      const formData = new FormData();
      formData.append('username', username);
      formData.append('password', password);

      const response = await axios.post(`${baseURL}/login/`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const newToken = response.data.data.access_token;
      console.log('Login successful, token received:', newToken);
      setToken(newToken);
      localStorage.setItem('authToken', newToken);
      setIsAuthenticated(true);
      // Remove this line since we're not using a login modal
    } catch (err) {
      console.error('Login error:', err);
      setAuthError('Invalid credentials. Please try again.');
    }
  };

  const handleLogout = () => {
    setToken('');
    localStorage.removeItem('authToken');
    setIsAuthenticated(false);
    // Remove this line since we're not using a login modal
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Detailed validation with specific error messages
    const missingFields = [];

    if (!targetDate) {
      missingFields.push("Target Date");
    }

    if (!hisaFile) {
      missingFields.push("Hisa1 File");
    }

    if (!hisaFile2) {
      missingFields.push("Hisa2 File");
    }

    if (!telcoFile) {
      missingFields.push("Telco File");
    }

    if (missingFields.length > 0) {
      const errorMsg = `
        <ul class="mb-0 ps-3">
          ${missingFields.map(field => `<li><strong>${field}</strong> is required</li>`).join('')}
        </ul>
      `;
      setErrorMessage(errorMsg);
      setShowErrorModal(true);
      return;
    }

    // Show success toast when all fields are valid
    setShowSuccessToast(true);

    setLoading(true);
    setErrorMessage('');

    const formData = new FormData();
    formData.append('mno', selectedProvider);
    formData.append('target_date', targetDate);
    formData.append('hisa_file', hisaFile);
    formData.append('hisa_file2', hisaFile2);
    formData.append('telco_file', telcoFile);
    if (telcoFile2) {
      formData.append('telco_file2', telcoFile2);
    }
    formData.append('use_transaction_id', useTransactionId.toString());

    try {
      const response = await axios.post(
        `${baseURL}/reconcile_logs/`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            'Authorization': `Bearer ${token}`
          },
        }
      );
      console.log('Reconciliation successful:', response.data);
      // Hide success toast when result is received
      setShowSuccessToast(false);
      setResult(response.data);
    } catch (err) {
      console.error('Reconciliation error:', err);
      // Hide success toast when an error occurs
      setShowSuccessToast(false);

      if (axios.isAxiosError(err)) {
        console.log('Error status:', err.response?.status);
        console.log('Error response:', err.response?.data);
      }

      if (axios.isAxiosError(err) && err.response?.status === 401) {
        handleLogout();
        setErrorMessage('Session expired. Please login again.');
        setShowErrorModal(true);
      } else if (axios.isAxiosError(err) && err.response?.status === 500) {
        const errorMsg = err.response.data.error || 'An unexpected error occurred';
        setErrorMessage(`
          <div class="d-flex align-items-center">
            <i class="bi bi-exclamation-circle-fill text-danger me-2 fs-5"></i>
            <strong>${errorMsg}</strong>
          </div>
        `);
        setShowErrorModal(true);
      } else {
        setErrorMessage(`
          <div class="d-flex align-items-center">
            <i class="bi bi-exclamation-circle-fill text-danger me-2 fs-5"></i>
            <strong>Error processing files. Please try again.</strong>
          </div>
          <div class="mt-2 text-muted">
            Check that your files are in the correct format and try again.
          </div>
        `);
        setShowErrorModal(true);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    setResult(null);
    setHisaFile(null);
    setHisaFile2(null);
    setTelcoFile(null);
    setTelcoFile2(null);
    setTargetDate('');
  };

  useEffect(() => {
    console.log('Token changed:', token);
    console.log('Is authenticated:', isAuthenticated);
  }, [token, isAuthenticated]);

  if (!isAuthenticated) {
    return (
      <>
        <div className="form-container">
          <Container>
            <Card className="shadow-lg border-0 rounded-4">
              <Card.Header className="bg-primary bg-gradient text-white p-4 rounded-top-4 border-0 text-center">
                <h4 className="mb-0">HISA Logs Reconciliation</h4>
                <p className="mb-0 mt-2 opacity-75 fw-light">Please login to continue</p>
              </Card.Header>
              <Card.Body className="p-4">
                <Form onSubmit={handleLogin}>
                  <div className="form-section mb-4">
                    <div className="input-group-custom">
                      <Form.Group className="mb-4">
                        <Form.Label className="fw-medium mb-2">Username</Form.Label>
                        <Form.Control
                          type="text"
                          value={username}
                          onChange={(e) => setUsername(e.target.value)}
                          required
                          className="form-control-custom"
                          placeholder="Enter your username"
                        />
                      </Form.Group>

                      <Form.Group className="mb-4">
                        <Form.Label className="fw-medium mb-2">Password</Form.Label>
                        <Form.Control
                          type="password"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          required
                          className="form-control-custom"
                          placeholder="Enter your password"
                        />
                      </Form.Group>
                    </div>
                    {authError && (
                      <Alert variant="danger" className="mt-3">
                        {authError}
                      </Alert>
                    )}
                  </div>

                  <div className="d-grid mt-4">
                    <Button
                      variant="primary"
                      type="submit"
                      size="lg"
                      className="submit-button"
                    >
                      <i className="bi bi-box-arrow-in-right me-2"></i>
                      Login
                    </Button>
                  </div>
                </Form>
              </Card.Body>
            </Card>
          </Container>
        </div>
      </>
    );
  }

  if (result) {
    return (
      <>
        <div className="form-container">
          <ReconciliationResults result={result} onBack={handleBack} />
        </div>
      </>
    );
  }

  return (
    <>
      <ErrorModal />
      <SuccessToast />
      <div className="form-container">
        <Container>
          <Card className="shadow-lg border-0 rounded-4">
            <Card.Header className="bg-primary bg-gradient text-white p-4 rounded-top-4 border-0 d-flex justify-content-between align-items-center">
              <div>
                <h4 className="mb-0">HISA Logs Reconciliation</h4>
                {/* <p className="mb-0 mt-2 opacity-75 fw-light">Upload your files to reconcile transactions</p> */}
              </div>
              <Button
                variant="outline-light"
                onClick={handleLogout}
                className="px-3 py-2"
              >
                <i className="bi bi-box-arrow-right me-2"></i>
                Logout
              </Button>
            </Card.Header>

            <Card.Body className="p-4">
              <Form onSubmit={handleSubmit} className="reconciliation-form">
                {/* Basic Information Section */}
                <div className="form-section mb-4">
                  <h5 className="section-title mb-4">Basic Information</h5>
                  <div className="input-group-custom">
                    <Form.Group className="mb-4">
                      <Form.Label className="fw-medium mb-2">Provider</Form.Label>
                      <Form.Select
                        value={selectedProvider}
                        onChange={(e) => setSelectedProvider(e.target.value)}
                        required
                        className="form-select-custom"
                      >
                        <option value="mtn">MTN</option>
                        <option value="airtel">Airtel</option>
                        <option value="glo">GLO</option>
                      </Form.Select>
                    </Form.Group>

                    <Form.Group className="mb-4">
                      <Form.Label className="fw-medium mb-2">Target Date</Form.Label>
                      <Form.Control
                        type="date"
                        value={targetDate}
                        onChange={(e) => setTargetDate(e.target.value)}
                        required
                        className="form-control-custom"
                      />
                    </Form.Group>
                  </div>
                </div>

                {/* File Upload Section */}
                <div className="form-section mb-4">
                  <h5 className="section-title mb-4">File Upload</h5>
                  <Row>
                    <Col md={6}>
                      <div className="file-upload-card">
                        <div className="file-upload-header mb-3">
                          <i className="bi bi-file-earmark-text file-icon"></i>
                          <h6 className="mb-0">Hisa1 File (Required)</h6>
                        </div>
                        <div className="upload-box">
                          <Form.Control
                            type="file"
                            onChange={(e) => setHisaFile((e.target as HTMLInputElement).files?.[0] || null)}
                            required
                            className="d-none"
                            id="hisaFile"
                          />
                          <label htmlFor="hisaFile" className="upload-label">
                            <i className="bi bi-cloud-upload upload-icon"></i>
                            <span className="upload-text">Click to upload or drag and drop</span>
                            {hisaFile && <span className="selected-file">{hisaFile.name}</span>}
                          </label>
                        </div>
                      </div>
                      <div className="file-upload-card mt-3">
                        <div className="file-upload-header mb-3">
                          <i className="bi bi-file-earmark-text file-icon"></i>
                          <h6 className="mb-0">Hisa2 File (Required)</h6>
                        </div>
                        <div className="upload-box">
                          <Form.Control
                            type="file"
                            onChange={(e) => setHisaFile2((e.target as HTMLInputElement).files?.[0] || null)}
                            required
                            className="d-none"
                            id="hisaFile2"
                          />
                          <label htmlFor="hisaFile2" className="upload-label">
                            <i className="bi bi-cloud-upload upload-icon"></i>
                            <span className="upload-text">Click to upload or drag and drop</span>
                            {hisaFile2 && <span className="selected-file">{hisaFile2.name}</span>}
                          </label>
                        </div>
                      </div>
                    </Col>
                    <Col md={6}>
                      <div className="file-upload-card">
                        <div className="file-upload-header mb-3">
                          <i className="bi bi-file-earmark-text file-icon"></i>
                          <h6 className="mb-0">Telco File (Required)</h6>
                        </div>
                        <div className="upload-box">
                          <Form.Control
                            type="file"
                            onChange={(e) => setTelcoFile((e.target as HTMLInputElement).files?.[0] || null)}
                            required
                            className="d-none"
                            id="telcoFile"
                          />
                          <label htmlFor="telcoFile" className="upload-label">
                            <i className="bi bi-cloud-upload upload-icon"></i>
                            <span className="upload-text">Click to upload or drag and drop</span>
                            {telcoFile && <span className="selected-file">{telcoFile.name}</span>}
                          </label>
                        </div>
                      </div>
                      <div className="file-upload-card mt-3">
                        <div className="file-upload-header mb-3">
                          <i className="bi bi-file-earmark-text file-icon"></i>
                          <h6 className="mb-0">Additional Telco File (Optional)</h6>
                        </div>
                        <div className="upload-box">
                          <Form.Control
                            type="file"
                            onChange={(e) => setTelcoFile2((e.target as HTMLInputElement).files?.[0] || null)}
                            className="d-none"
                            id="telcoFile2"
                          />
                          <label htmlFor="telcoFile2" className="upload-label">
                            <i className="bi bi-cloud-upload upload-icon"></i>
                            <span className="upload-text">Click to upload or drag and drop</span>
                            {telcoFile2 && <span className="selected-file">{telcoFile2.name}</span>}
                          </label>
                        </div>
                      </div>
                    </Col>
                  </Row>
                </div>

                {/* Options Section */}
                <div className="form-section mb-4">
                  <h5 className="section-title mb-4">Additional Options</h5>
                  <div className="options-card">
                    <Form.Check
                      type="checkbox"
                      id="transactionIdCheck"
                      checked={useTransactionId}
                      onChange={(e) => setUseTransactionId(e.target.checked)}
                      label="Use Transaction ID for matching"
                      className="custom-checkbox"
                    />
                  </div>
                </div>

                {/* Submit Button */}
                <div className="d-grid mt-5">
                  <Button
                    variant="primary"
                    type="submit"
                    disabled={loading}
                    size="lg"
                    className="submit-button"
                  >
                    {loading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Processing...
                      </>
                    ) : (
                      <>
                        <i className="bi bi-arrow-repeat me-2"></i>
                        Reconcile Transactions
                      </>
                    )}
                  </Button>
                </div>
              </Form>
            </Card.Body>
          </Card>
        </Container>
      </div>
    </>
  );
}

export default App;
