import json

input_file = "sample.txt"
output_file = "cleaned_output.json"

all_data = 0
cleaned_data = 0
unclean_data = 0


def clean_txt_file(input_file):
    cleaned_data = []
    total_count = 0
    cleaned_count = 0
    unclean_count = 0

    with open(input_file, "r", encoding="utf-8") as user_file, open(output_file, "w", encoding="utf-8") as outfile:
        for line_number, line in enumerate(user_file, 1):
            total_count += 1
            try:
                # Load the outer JSON object
                entry = json.loads(line.strip())

                # Try to decode the embedded 'request' JSON string
                if "request" in entry and isinstance(entry["request"], str):
                    entry["request"] = json.loads(entry["request"])

                # Write the cleaned JSON to output as a line
                outfile.write(json.dumps(entry) + "\n")
                cleaned_data.append(entry)
                cleaned_count += 1
        
            except json.JSONDecodeError as e:
                unclean_count += 1
                print(f"[Line {line_number}] Skipped due to error: {e}")
    return cleaned_data, total_count, cleaned_count, unclean_count


if __name__ == "__main__":
    cleaner = clean_txt_file(input_file)
    
    print(f"Total data: {cleaner[1]}")
    print(f"Cleaned data: {cleaner[2]}")
    print(f"Unclean data: {cleaner[3]}")
